<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\Spot
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $likes 点赞数
 * @property int $ord 排序值
 * @property int $rec 推荐值
 * @property int $stars 收藏数
 * @property int $status 状态
 * @property int $views 浏览量
 * @property mixed $hours 开放时间
 * @property mixed $images 景点图片
 * @property mixed $location 位置坐标
 * @property mixed $tickets 门票信息
 * @property string $address 详细地址
 * @property string $name 景点名称
 * @property string $phone 联系电话
 * @property-read \app\model\User[] $likeUsers 点赞用户
 * @property-read \app\model\User[] $like_users
 * @property-read \app\model\User[] $starUsers 收藏用户
 * @property-read \app\model\User[] $star_users
 * @property-read \app\model\User[] $members 景点成员
 * @property-read \app\model\User $owner 景点所有者
 */
class Spot extends Model
{
    // 景点状态常量
    const STATUS_PENDING  = 0; // 待审核
    const STATUS_NORMAL   = 1; // 正常
    const STATUS_REJECTED = 2; // 已拒绝
    const STATUS_DISABLED = 3; // 禁用

    protected $json = ['images', 'hours', 'location', 'tickets'];
    protected $jsonAssoc = true;

    /**
     * 获取点赞用户关联
     */
    public function likeUsers()
    {
        return $this->belongsToMany(User::class, SpotLike::class, 'user_id', 'spot_id');
    }

    /**
     * 获取收藏用户关联
     */
    public function starUsers()
    {
        return $this->belongsToMany(User::class, SpotStar::class, 'user_id', 'spot_id');
    }

    /**
     * 获取景点成员关联
     */
    public function members()
    {
        return $this->belongsToMany(User::class, SpotMember::class, 'user_id', 'spot_id');
    }

    /**
     * 获取景点所有者
     */
    public function owner()
    {
        return $this->hasOneThrough(User::class, SpotMember::class, 'spot_id', 'id', 'id', 'user_id')
            ->wherePivot('access_level', SpotMember::OWNER);
    }
}
