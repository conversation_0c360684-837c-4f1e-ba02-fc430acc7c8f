<?php

namespace app\controller\api\hotel;

use app\controller\api\Controller;
use app\model\Hotel;
use app\model\HotelMember;
use app\model\OperationLog;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Post;
use think\annotation\route\Put;
use think\exception\ValidateException;
use yunwuxin\auth\middleware\Authentication;

class IndexController extends Controller
{
    #[Get('hotel')]
    public function index()
    {
        $query = Hotel::where('status', Hotel::STATUS_NORMAL);

        $sort = $this->request->param('sort');
        if ($sort) {
            $order = $this->request->param('order', 'desc');
            if ($sort == 'rating') {
                $query->orderRaw("JSON_EXTRACT(rating,'$.score') {$order}");
            } else {
                $query->order($sort, $order);
            }
        } else {
            $query->order('rec', 'desc')
                ->order('ord', 'desc')
                ->order('id', 'desc');
        }

        $this->filterFields($query, [
            'district' => function ($query, $value) {
                $query->where('location->district', $value);
            },
            'type'
        ]);

        $hotels = $query->paginate();

        return json($hotels);
    }

    #[Post('hotel')]
    #[Middleware(Authentication::class)]
    public function save()
    {
        $hotel = $this->user->hotels()->find();
        if ($hotel) {
            throw new ValidateException('您已入驻过酒店');
        }

        $data = $this->validateHotelData();

        $hotel = $this->user->hotels()->save($data, [
            'access_level' => HotelMember::OWNER,
        ]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_CREATE,
            OperationLog::MODULE_HOTEL,
            "创建酒店 {$data['name']}",
            $hotel->id,
            'hotel'
        );

        return json($hotel);
    }

    #[Put('hotel')]
    #[Middleware(Authentication::class)]
    public function update()
    {
        /** @var Hotel $hotel */
        $hotel = $this->user->hotels()->find();
        if (!$hotel) {
            throw new ValidateException('您还没有酒店');
        }

        $data = $this->validateHotelData();

        if ($hotel->status == Hotel::STATUS_REJECTED) {
            $data['status'] = Hotel::STATUS_PENDING;
        }

        $hotel->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_HOTEL,
            "更新酒店 {$hotel->name} 的信息",
            $hotel->id,
            'hotel'
        );

        return json($hotel);
    }

    /**
     * 验证酒店数据
     * @return array 验证后的数据
     */
    private function validateHotelData()
    {
        $data = $this->validate([
            'name|酒店名称' => 'require',
            'type|酒店类型' => 'require',
            'images|酒店图片' => 'require|array',
            'location|位置坐标' => 'require',
            'address|详细地址' => 'require',
            'path|携程地址' => '',
            'price|价格' => 'require|integer',
            'rating|评分' => 'array',
        ]);

        // 添加地区信息
        $data['location']['district'] = get_district($data['location']['latitude'], $data['location']['longitude']);

        return $data;
    }
}
