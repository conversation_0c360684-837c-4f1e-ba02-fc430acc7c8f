<?php

namespace app\controller\api\hotel;

use app\controller\api\Controller;
use app\model\Hotel;
use app\model\HotelMember;
use app\model\OperationLog;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Post;
use think\annotation\route\Put;
use think\exception\ValidateException;
use yunwuxin\auth\middleware\Authentication;

#[Middleware(Authentication::class)]
class OwnerController extends Controller
{
    use WithHotel;

    #[Get('hotel/owner')]
    public function index()
    {
        /** @var Hotel $hotel */
        $hotel = $this->user->hotels()->find();

        return json($hotel);
    }

    #[Post('hotel/owner')]
    public function save()
    {
        $hotel = $this->user->hotels()->find();
        if ($hotel) {
            throw new ValidateException('您已入驻过酒店');
        }

        $data = $this->validateHotelData();

        $hotel = $this->user->hotels()->save($data, [
            'access_level' => HotelMember::OWNER,
        ]);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_CREATE,
            OperationLog::MODULE_HOTEL,
            "创建酒店 {$data['name']}",
            $hotel->id,
            'hotel'
        );

        return json($hotel);
    }

    #[Put('hotel/owner')]
    public function update()
    {
        /** @var Hotel $hotel */
        $hotel = $this->user->hotels()->find();
        if (!$hotel) {
            throw new ValidateException('您还没有酒店');
        }

        $data = $this->validateHotelData();

        if ($hotel->status == Hotel::STATUS_REJECTED) {
            $data['status'] = Hotel::STATUS_PENDING;
        }

        $hotel->save($data);

        // 记录操作日志
        $this->log(
            OperationLog::ACTION_UPDATE,
            OperationLog::MODULE_HOTEL,
            "更新酒店 {$hotel->name} 的信息",
            $hotel->id,
            'hotel'
        );

        return json($hotel);
    }

    /**
     * 验证酒店数据
     * @return array 验证后的数据
     */
    private function validateHotelData()
    {
        $data = $this->validate([
            'name|酒店名称' => 'require',
            'type|酒店类型' => 'require',
            'images|酒店图片' => 'require|array',
            'location|位置坐标' => 'require',
            'address|详细地址' => 'require',
            'path|携程地址' => '',
            'price|价格' => 'require|integer',
            'rating|评分' => 'array',
        ]);

        // 添加地区信息
        $data['location']['district'] = get_district($data['location']['latitude'], $data['location']['longitude']);

        return $data;
    }
}
